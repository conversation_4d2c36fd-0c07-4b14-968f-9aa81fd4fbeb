class IPromise {
  static PENDING = "pending";
  static FULFILLED = "fulfilled";
  static REJECTED = "rejected";

  constructor(executor) {
    /** 当前状态 */
    this.state = myPromise.PENDING;
    /** 成功的值 */
    this.value = undefined;
    /** 失败的原因 */
    this.reason = undefined;
    /** 成功回调队列 */
    this.onFulfilledCallbacks = [];
    /** 失败回调队列 */
    this.onRejectedCallbacks = [];

    const resolve = (value) => {
      if (this.state === IPromise.PENDING) {
        this.state = IPromise.FULFILLED;
        this.value = value;
        this.onFulfilledCallbacks.forEach((cb) => cb(value));
      }
    };

    const reject = (reason) => {
      if (this.state === IPromise.PENDING) {
        this.state = IPromise.REJECTED;
        this.reason = reason;
        this.onRejectedCallbacks.forEach((cb) => cb(reason));
      }
    };

    try {
      executor(resolve, reject);
    } catch (error) {
      reject(error);
    }
  }
}

export default IPromise;
